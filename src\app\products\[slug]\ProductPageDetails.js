"use client";
import StoreIcons from "@/components/Footer/StoreIcons";
import YouTubeVideo from "@/components/YouTubeVideo";
import Image from "next/image";
import { useState } from "react";
import styles from "./styles.module.css";

const ProductPageDetails = ({ game }) => {
  const [showVideo, setShowVideo] = useState(false);

  // Extract YouTube video ID from URL
  const getYouTubeVideoId = (url) => {
    if (!url) return null;
    const match = url.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/);
    return match ? match[1] : null;
  };

  const videoId = getYouTubeVideoId(game.videoUrl);
  // Prepare store icons data for StoreIcons component
  const storeIconsData = [];

  if (game.storeLinks?.appStore) {
    storeIconsData.push({
      src: "/images/footer/socialIcons/Appstore.webp",
      alt: "Download on App Store",
      onClick: () => window.open(game.storeLinks.appStore, "_blank", "noopener,noreferrer"),
    });
  }

  if (game.storeLinks?.playStore) {
    storeIconsData.push({
      src: "/images/footer/socialIcons/Playstore.webp",
      alt: "Get it on Google Play",
      onClick: () => window.open(game.storeLinks.playStore, "_blank", "noopener,noreferrer"),
    });
  }

  if (game.storeLinks?.amazonStore) {
    storeIconsData.push({
      src: "/images/footer/socialIcons/Amazon.webp",
      alt: "Available on Amazon",
      onClick: () => window.open(game.storeLinks.amazonStore, "_blank", "noopener,noreferrer"),
    });
  }

  if (game.storeLinks?.webStore) {
    storeIconsData.push({
      src: "/images/footer/socialIcons/Web.webp",
      alt: "Play on Web",
      onClick: () => window.open(game.storeLinks.webStore, "_blank", "noopener,noreferrer"),
    });
  }

  return (
    <div className={styles.productPageWrapper}>
      {/* Hero Section */}
      <div className={styles.heroSection}>
        <div className={styles.heroContent}>
          <div className={styles.heroLeft}>
            <h1 className={styles.gameTitle}>{game.gameName}</h1>
            <p className={styles.gameDescription}>{game.gameDesc}</p>

            {/* Store Icons */}
            {/* <div className={styles.storeIconsSection}> */}
            <StoreIcons icons={storeIconsData} styleClass={styles.productPageStoreIcons} />
            {/* </div> */}

            {/* Rating */}
            <div className={styles.ratingSection}>
              <div className={styles.ratingStars}>
                <span className={styles.ratingNumber}>4.0</span>
                <div className={styles.stars}>
                  {[1, 2, 3, 4].map((star) => (
                    <span key={star} className={styles.starFilled}>
                      ★
                    </span>
                  ))}
                  <span className={styles.starEmpty}>★</span>
                </div>
                <span className={styles.reviewCount}>100+ Reviews</span>
              </div>
            </div>
          </div>

          <div className={styles.heroRight}>
            <div className={styles.gameImageWrapper}>
              <Image
                src={game.gameThumbnailImg}
                alt={game.gameName}
                width={400}
                height={300}
                className={styles.gameImage}
                priority
              />
            </div>
          </div>
        </div>
      </div>

      <div className={styles.productWrapperInner}>
        {/* Video Section */}
        <div className={styles.videoSection}>
          {showVideo && videoId ? (
            <div className={styles.videoContainer}>
              <YouTubeVideo
                videoId={videoId}
                title={`${game.gameName} Gameplay Video`}
                width="100%"
                height="100%"
              />
            </div>
          ) : (
            <button
              className={styles.videoPlaceholder}
              onClick={() => setShowVideo(true)}
              disabled={!videoId}
              aria-label={videoId ? "Watch gameplay video" : "Video not available"}
            >
              <div className={styles.playButton}>
                <div className={styles.playIcon}>▶</div>
              </div>
              {videoId && (
                <div className={styles.videoOverlay}>
                  <span>Watch Gameplay Video</span>
                </div>
              )}
            </button>
          )}

          {/* Game descriptions */}
          <div className={styles.gameDescriptions}>
            {game.gameContentInside?.description1 && (
              <p className={styles.descriptionText}>{game.gameContentInside.description1}</p>
            )}
            {game.gameContentInside?.description2 && (
              <p className={styles.descriptionText}>{game.gameContentInside.description2}</p>
            )}
          </div>
        </div>

        {/* Learning Outcomes Section */}
        <div className={styles.learningSection}>
          <h2 className={styles.learningSectionTitle}>What Kid&apos;s Learn?</h2>

          <div className={styles.learningOutcomes}>
            {/* Use specific learning outcomes if available, otherwise fallback to game skill and interests */}
            {game.learningOutcomes ? (
              game.learningOutcomes.map((outcome, index) => (
                <div key={index} className={styles.learningCard}>
                  <div className={styles.learningIconPlaceholder}>
                    <span>learning outcome icons</span>
                  </div>
                  <h3 className={styles.learningCardTitle}>{outcome}</h3>
                </div>
              ))
            ) : (
              <>
                <div className={styles.learningCard}>
                  <div className={styles.learningIconPlaceholder}>
                    <span>learning outcome icons</span>
                  </div>
                  <h3 className={styles.learningCardTitle}>{game.gameSkill}</h3>
                </div>
                {game.interests?.map((interest, index) => (
                  <div key={index} className={styles.learningCard}>
                    <div className={styles.learningIconPlaceholder}>
                      <span>learning outcome icons</span>
                    </div>
                    <h3 className={styles.learningCardTitle}>
                      {interest.toLowerCase().replace("_", " ")}
                    </h3>
                  </div>
                ))}
              </>
            )}
          </div>

          {/* Game Screenshots */}
          <div className={styles.screenshotsSection}>
            {game.gamePopinImages?.map((image, index) => (
              <div key={index} className={styles.screenshotWrapper}>
                <Image
                  src={image}
                  alt={`${game.gameName} screenshot ${index + 1}`}
                  width={200}
                  height={356}
                  className={styles.screenshot}
                />
              </div>
            ))}
          </div>
        </div>

        {/* Reviews Section */}
        {game.reviews && game.reviews.length > 0 && (
          <div className={styles.reviewsSection}>
            <h2 className={styles.reviewsSectionTitle}>What Parents Say</h2>
            <div className={styles.reviewsGrid}>
              {game.reviews.map((review, index) => (
                <div key={index} className={styles.reviewCard}>
                  <div className={styles.reviewHeader}>
                    <h4 className={styles.reviewAuthor}>{review.author}</h4>
                    {review.date && <span className={styles.reviewDate}>{review.date}</span>}
                  </div>
                  {review.title && <h5 className={styles.reviewTitle}>{review.title}</h5>}
                  <p className={styles.reviewContent}>{review.content}</p>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProductPageDetails;
