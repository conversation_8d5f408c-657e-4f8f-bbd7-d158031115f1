"use client";
import StoreIcons from "@/components/Footer/StoreIcons";
import Image from "next/image";
import styles from "./styles.module.css";

const ProductPageDetails = ({ game }) => {
  // Prepare store icons data for StoreIcons component
  const storeIconsData = [];

  if (game.storeLinks?.appStore) {
    storeIconsData.push({
      src: "/images/footer/socialIcons/Appstore.webp",
      alt: "Download on App Store",
      onClick: () => window.open(game.storeLinks.appStore, "_blank", "noopener,noreferrer"),
    });
  }

  if (game.storeLinks?.playStore) {
    storeIconsData.push({
      src: "/images/footer/socialIcons/Playstore.webp",
      alt: "Get it on Google Play",
      onClick: () => window.open(game.storeLinks.playStore, "_blank", "noopener,noreferrer"),
    });
  }

  return (
    <div className={styles.productPageWrapper}>
      {/* Hero Section */}
      <div className={styles.heroSection}>
        <div className={styles.heroContent}>
          <div className={styles.heroLeft}>
            <h1 className={styles.gameTitle}>{game.gameName}</h1>
            <p className={styles.gameDescription}>{game.gameDesc}</p>

            {/* Store Icons */}
            {/* <div className={styles.storeIconsSection}> */}
            <StoreIcons icons={storeIconsData} styleClass={styles.productPageStoreIcons} />
            {/* </div> */}

            {/* Rating */}
            <div className={styles.ratingSection}>
              <div className={styles.ratingStars}>
                <span className={styles.ratingNumber}>4.0</span>
                <div className={styles.stars}>
                  {[1, 2, 3, 4].map((star) => (
                    <span key={star} className={styles.starFilled}>
                      ★
                    </span>
                  ))}
                  <span className={styles.starEmpty}>★</span>
                </div>
                <span className={styles.reviewCount}>100+ Reviews</span>
              </div>
            </div>
          </div>

          <div className={styles.heroRight}>
            <div className={styles.gameImageWrapper}>
              <Image
                src={game.gameThumbnailImg}
                alt={game.gameName}
                width={400}
                height={300}
                className={styles.gameImage}
                priority
              />
            </div>
          </div>
        </div>
      </div>

      <div className={styles.productWrapperInner}>
        {/* Video Section */}
        <div className={styles.videoSection}>
          <div className={styles.videoPlaceholder}>
            <div className={styles.playButton}>
              <div className={styles.playIcon}>▶</div>
            </div>
          </div>

          {/* Game descriptions */}
          <div className={styles.gameDescriptions}>
            {game.gameContentInside?.description1 && (
              <p className={styles.descriptionText}>{game.gameContentInside.description1}</p>
            )}
            {game.gameContentInside?.description2 && (
              <p className={styles.descriptionText}>{game.gameContentInside.description2}</p>
            )}
          </div>
        </div>

        {/* Learning Outcomes Section */}
        <div className={styles.learningSection}>
          <h2 className={styles.learningSectionTitle}>What Kid&apos;s Learn?</h2>

          <div className={styles.learningOutcomes}>
            {/* Learning outcome cards - we'll create placeholder ones based on game skill */}
            <div className={styles.learningCard}>
              <div className={styles.learningIconPlaceholder}>
                <span>learning outcome icons</span>
              </div>
              <h3 className={styles.learningCardTitle}>{game.gameSkill}</h3>
            </div>

            {/* Additional learning cards based on interests */}
            {game.interests?.slice(0, 2).map((interest, index) => (
              <div key={index} className={styles.learningCard}>
                <div className={styles.learningIconPlaceholder}>
                  <span>learning outcome icons</span>
                </div>
                <h3 className={styles.learningCardTitle}>
                  {interest.toLowerCase().replace("_", " ")}
                </h3>
              </div>
            ))}
          </div>

          {/* Game Screenshots */}
          <div className={styles.screenshotsSection}>
            {game.gamePopinImages?.slice(0, 4).map((image, index) => (
              <div key={index} className={styles.screenshotWrapper}>
                <Image
                  src={image}
                  alt={`${game.gameName} screenshot ${index + 1}`}
                  width={200}
                  height={356}
                  className={styles.screenshot}
                />
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductPageDetails;
