/* Product Page Wrapper */
.productPageWrapper {
  font-family: var(--font-poppins);
}

.productWrapperInner {
  margin: 0 auto;
  padding: 0 8rem;
}

/* Hero Section */
.heroSection {
  background: linear-gradient(135deg, #a8e6cf 0%, #88d8c0 100%);
  padding: 40px;
  margin-bottom: 40px;
  position: relative;
  overflow: hidden;
}

.heroContent {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  align-items: center;
}

.heroLeft {
  z-index: 2;
  display: flex;
  flex-direction: column;
}

.gameTitle {
  font-size: 3rem;
  font-weight: bold;
  color: #333;
  margin: 0 0 16px 0;
  line-height: 1.2;
}

.gameDescription {
  font-size: 1.2rem;
  color: #555;
  margin: 0 0 30px 0;
  line-height: 1.5;
}

/* Store Icons Section */
.storeIconsSection {
  margin: 30px 0;
}

.productPageStoreIcons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  max-width: 400px;
  align-self: flex-start;
    margin-left: -1rem;
    padding-left: 0rem;
}

/* Rating Section */
.ratingSection {
  margin-top: 20px;
}

.ratingStars {
  display: flex;
  align-items: center;
  gap: 8px;
}

.ratingNumber {
  font-size: 1.5rem;
  font-weight: bold;
  color: #333;
}

.stars {
  display: flex;
  gap: 2px;
}

.starFilled {
  color: #ffd700;
  font-size: 1.2rem;
}

.starEmpty {
  color: #ddd;
  font-size: 1.2rem;
}

.reviewCount {
  font-size: 0.9rem;
  color: #666;
}

/* Hero Right */
.heroRight {
  display: flex;
  justify-content: center;
  align-items: center;
}

.gameImageWrapper {
  position: relative;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.gameImage {
  width: 100%;
  height: auto;
  border-radius: 15px;
}

/* Video Section */
.videoSection {
  margin-bottom: 50px;
}

.videoPlaceholder {
  width: 100%;
  height: 300px;
  background-color: #f0f0f0;
  border-radius: 15px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 30px;
  position: relative;
  cursor: pointer;
}

.playButton {
  width: 80px;
  height: 80px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  transition: transform 0.2s;
}

.playButton:hover {
  transform: scale(1.1);
}

.playIcon {
  font-size: 24px;
  color: #1f5dfe;
  margin-left: 4px;
}

.gameDescriptions {
  max-width: 800px;
  margin: 0 auto;
}

.descriptionText {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #555;
  margin-bottom: 20px;
  text-align: center;
}

/* Learning Section */
.learningSection {
  margin-bottom: 50px;
}

.learningSectionTitle {
  font-size: 2.5rem;
  font-weight: bold;
  text-align: center;
  color: #333;
  margin-bottom: 40px;
}

.learningOutcomes {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
  margin-bottom: 50px;
}

.learningCard {
  text-align: center;
  padding: 20px;
}

.learningIconPlaceholder {
  width: 120px;
  height: 120px;
  background-color: #f0f0f0;
  border-radius: 15px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto 20px auto;
  font-size: 12px;
  color: #999;
}

.learningCardTitle {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin: 0;
  text-transform: capitalize;
}

/* Screenshots Section */
.screenshotsSection {
  display: flex;
  gap: 20px;
  margin-top: 40px;
  overflow-x: auto;
  overflow-y: hidden;
  padding: 10px 0;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
  scrollbar-color: #ccc transparent;
}

.screenshotsSection::-webkit-scrollbar {
  height: 6px;
}

.screenshotsSection::-webkit-scrollbar-track {
  background: transparent;
}

.screenshotsSection::-webkit-scrollbar-thumb {
  background-color: #ccc;
  border-radius: 3px;
}

.screenshotsSection::-webkit-scrollbar-thumb:hover {
  background-color: #999;
}

.screenshotWrapper {
  flex: 0 0 auto;
  width: 200px;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.screenshot {
  width: 100%;
  height: auto;
  border-radius: 15px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .productPageWrapper {
    padding: 0 15px;
  }

  .heroSection {
    padding: 30px 20px;
  }

  .heroContent {
    grid-template-columns: 1fr;
    gap: 30px;
    text-align: center;
  }

  .gameTitle {
    font-size: 2.2rem;
  }

  .gameDescription {
    font-size: 1.1rem;
  }

  .productPageStoreIcons {
    grid-template-columns: repeat(2, 1fr);
    max-width: 300px;
    margin: 0 auto;
  }

  .learningSectionTitle {
    font-size: 2rem;
  }

  .learningOutcomes {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .videoPlaceholder {
    height: 200px;
  }

  .playButton {
    width: 60px;
    height: 60px;
  }

  .playIcon {
    font-size: 18px;
  }
}

/* Extra small screens */
@media (max-width: 480px) {
  .gameTitle {
    font-size: 1.8rem;
  }

  .heroSection {
    padding: 20px 15px;
  }

  .productPageStoreIcons {
    grid-template-columns: 1fr;
    max-width: 200px;
  }
}
