"use client";
import { gamesData, themeFilters } from "@/constants";
import useScrollAnimation from "@/hooks/useScrollAnimation";
import { generateGameSlug } from "@/utils/helperFunctions";
import Image from "next/image";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import Carousel from "../../components/CarouselProduct";
import styles from "./styles.module.css";

const ProductsDetails = () => {
  const [selectedFilter, setSelectedFilter] = useState("All");
  const router = useRouter();
  const searchParams = useSearchParams();

  const handleGameClick = (game) => {
    const slug = generateGameSlug(game.gameDesc);
    router.push(`/products/${slug}`);
  };

  const handleFilterClick = (filter) => {
    setSelectedFilter(filter);
  };

  const filteredGames =
    selectedFilter === "All"
      ? gamesData
      : gamesData.filter((game) => game.interests.includes(selectedFilter.toUpperCase()));

  useEffect(() => {
    if (searchParams.get("theme")) {
      const section = document.getElementById("target-section");
      handleFilterClick(searchParams.get("theme"));
      section.scrollIntoView({ behavior: "smooth" });
    }
  }, [searchParams]);

  const setRef = useScrollAnimation(
    {
      duration: 1,
      ease: "power3.out",
      start: "top bottom",
    },
    []
  );

  return (
    <>
      <main className={styles.productsPageWrapper}>
        <Carousel />
        <div className={styles.productFilterGamesWrapper}>
          <div
            id="target-section"
            className={styles.filterParameterWrapper}
            // ref={(el) => setRef(el)}
          >
            {themeFilters.map((filter, index) => (
              <p
                key={index}
                onClick={() => handleFilterClick(filter)}
                className={selectedFilter === filter ? styles.activeFilter : ""}
              >
                {filter}
              </p>
            ))}
          </div>
          <section className={styles.gameCardsWrapper}>
            {filteredGames.map((game) => {
              return (
                <div
                  key={game.gameName}
                  onClick={() => handleGameClick(game)}
                  className={styles.productCards}
                  // ref={(el) => setRef(el)}
                >
                  <Image
                    src={game.gameThumbnailImg}
                    alt={game.gameName}
                    className={styles.gameCardsImg}
                  />
                  <p className={styles.gameName}>{game.gameName}</p>
                  <p className={styles.gameDescription}>{game.gameDesc}</p>
                  <div className={styles.gameSkillsWrapper}>
                    <p>{game.gameSkill}</p>
                  </div>
                  <button className={styles.gameCardBtn}>Learn More</button>
                </div>
              );
            })}
          </section>
        </div>
      </main>
    </>
  );
};

export default ProductsDetails;
